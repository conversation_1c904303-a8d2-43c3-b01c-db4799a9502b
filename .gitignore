# Node.js
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp/
.pnp.js

# Environment variables (CRITICAL for security)
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# TypeScript build artifacts
*.tsbuildinfo

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.sock
*.vscode-test

# Coverage
coverage/
.nyc_output/

# Editor/IDE specific
.vscode/
.idea/
.DS_Store
*.sublime-project
*.sublime-workspace

# OS generated files
.Trash*
ehthumbs.db
Thumbs.db